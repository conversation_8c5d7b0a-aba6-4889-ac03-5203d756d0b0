#!/bin/bash

# 测试配置加载功能的脚本

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 配置文件路径
CONFIG_SHARED="validation/configs/config_shared.yaml"
CONFIG_VLLM="validation/configs/config_vllm.yaml"

# 从YAML文件读取配置值的函数
read_yaml_value() {
    local file="$1"
    local key="$2"
    local default="$3"
    
    if [ ! -f "$file" ]; then
        echo "$default"
        return
    fi
    
    # 使用Python读取YAML值
    local value=$(python3 -c "
import yaml
import sys
try:
    with open('$file', 'r') as f:
        config = yaml.safe_load(f)
    
    # 支持嵌套键，如 'replica_config.output_dir'
    keys = '$key'.split('.')
    result = config
    for k in keys:
        if isinstance(result, dict) and k in result:
            result = result[k]
        else:
            result = None
            break
    
    if result is not None:
        print(result)
    else:
        print('$default')
except Exception as e:
    print('$default')
" 2>/dev/null)
    
    if [ -z "$value" ]; then
        echo "$default"
    else
        echo "$value"
    fi
}

# 测试配置读取
test_config_reading() {
    print_info "测试配置文件读取功能..."
    
    # 检查Python和PyYAML
    if ! command -v python3 &> /dev/null; then
        print_error "Python3不可用"
        return 1
    fi
    
    if ! python3 -c "import yaml" 2>/dev/null; then
        print_error "PyYAML不可用"
        return 1
    fi
    
    print_success "Python3和PyYAML可用"
    
    # 测试读取共享配置
    if [ -f "$CONFIG_SHARED" ]; then
        print_info "测试读取共享配置文件: $CONFIG_SHARED"
        
        local device=$(read_yaml_value "$CONFIG_SHARED" "device" "unknown")
        local network_device=$(read_yaml_value "$CONFIG_SHARED" "network_device" "unknown")
        local tensor_parallel_size=$(read_yaml_value "$CONFIG_SHARED" "tensor_parallel_size" "unknown")
        local model_name=$(read_yaml_value "$CONFIG_SHARED" "model_name" "unknown")
        local max_num_seqs=$(read_yaml_value "$CONFIG_SHARED" "max_num_seqs" "unknown")
        
        echo "  设备类型: $device"
        echo "  网络设备: $network_device"
        echo "  张量并行度: $tensor_parallel_size"
        echo "  模型名称: $model_name"
        echo "  最大序列数: $max_num_seqs"
        
        if [ "$device" != "unknown" ] && [ "$network_device" != "unknown" ]; then
            print_success "共享配置读取成功"
        else
            print_warning "部分共享配置读取失败"
        fi
    else
        print_error "共享配置文件不存在: $CONFIG_SHARED"
        return 1
    fi
    
    # 测试读取vLLM配置
    if [ -f "$CONFIG_VLLM" ]; then
        print_info "测试读取vLLM配置文件: $CONFIG_VLLM"
        
        local model_path=$(read_yaml_value "$CONFIG_VLLM" "model" "unknown")
        
        echo "  模型路径: $model_path"
        
        if [ "$model_path" != "unknown" ]; then
            print_success "vLLM配置读取成功"
        else
            print_warning "vLLM配置读取失败"
        fi
    else
        print_error "vLLM配置文件不存在: $CONFIG_VLLM"
        return 1
    fi
    
    return 0
}

# 测试完整配置加载流程
test_full_config_loading() {
    print_info "测试完整配置加载流程..."
    
    # 模拟run_cpu_profiling.sh中的配置加载
    local DEVICE=$(read_yaml_value "$CONFIG_SHARED" "device" "a100")
    local NETWORK_DEVICE=$(read_yaml_value "$CONFIG_SHARED" "network_device" "a100_pairwise_nvlink")
    local TENSOR_PARALLEL_SIZE=$(read_yaml_value "$CONFIG_SHARED" "tensor_parallel_size" "1")
    local MODEL_NAME=$(read_yaml_value "$CONFIG_SHARED" "model_name" "meta-llama/Meta-Llama-3-8B")
    local MODEL_PATH=$(read_yaml_value "$CONFIG_VLLM" "model" "/share_data/llm_weights/Meta-Llama-3-8B")
    local MAX_BATCH_SIZE=$(read_yaml_value "$CONFIG_SHARED" "prediction_max_batch_size" "512")
    
    local OUTPUT_DIR="data/profiling/cpu_overhead/${NETWORK_DEVICE}"
    
    print_info "加载的配置:"
    echo "  设备类型: $DEVICE"
    echo "  网络设备: $NETWORK_DEVICE"
    echo "  张量并行度: $TENSOR_PARALLEL_SIZE"
    echo "  模型名称: $MODEL_NAME"
    echo "  模型路径: $MODEL_PATH"
    echo "  最大批次大小: $MAX_BATCH_SIZE"
    echo "  输出目录: $OUTPUT_DIR"
    
    # 验证关键配置是否合理
    if [ -n "$DEVICE" ] && [ -n "$NETWORK_DEVICE" ] && [ -n "$MODEL_NAME" ] && [ -n "$MODEL_PATH" ]; then
        print_success "配置加载完整"
        return 0
    else
        print_error "配置加载不完整"
        return 1
    fi
}

# 主测试函数
main() {
    print_info "开始测试配置加载功能..."
    echo ""
    
    # 检查是否在正确的目录
    if [ ! -f "$CONFIG_SHARED" ]; then
        print_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 测试配置读取
    if ! test_config_reading; then
        print_error "配置读取测试失败"
        exit 1
    fi
    
    echo ""
    
    # 测试完整配置加载
    if ! test_full_config_loading; then
        print_error "完整配置加载测试失败"
        exit 1
    fi
    
    echo ""
    print_success "所有测试通过！配置加载功能正常。"
}

# 运行测试
main "$@"
