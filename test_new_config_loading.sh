#!/bin/bash

# 测试新的配置加载功能

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 测试parse_yaml.sh脚本
test_parse_yaml_script() {
    print_info "测试parse_yaml.sh脚本..."
    
    if [ ! -f "validation/parse_yaml.sh" ]; then
        print_error "parse_yaml.sh脚本不存在"
        return 1
    fi
    
    # 加载脚本
    source validation/parse_yaml.sh
    
    # 测试解析共享配置
    if parse_yaml "validation/configs/config_shared.yaml" "TEST_SHARED" true; then
        print_success "共享配置解析成功"
        
        # 检查一些关键变量
        if [ -n "$TEST_SHARED_device" ]; then
            print_info "设备类型: $TEST_SHARED_device"
        else
            print_warning "未找到设备类型配置"
        fi
        
        if [ -n "$TEST_SHARED_model_name" ]; then
            print_info "模型名称: $TEST_SHARED_model_name"
        else
            print_warning "未找到模型名称配置"
        fi
        
        if [ -n "$TEST_SHARED_network_device" ]; then
            print_info "网络设备: $TEST_SHARED_network_device"
        else
            print_warning "未找到网络设备配置"
        fi
    else
        print_error "共享配置解析失败"
        return 1
    fi
    
    # 测试解析vLLM配置
    if parse_yaml "validation/configs/config_vllm.yaml" "TEST_VLLM" true; then
        print_success "vLLM配置解析成功"
        
        if [ -n "$TEST_VLLM_model" ]; then
            print_info "模型路径: $TEST_VLLM_model"
        else
            print_warning "未找到模型路径配置"
        fi
    else
        print_error "vLLM配置解析失败"
        return 1
    fi
    
    return 0
}

# 测试配置验证函数
test_config_validation() {
    print_info "测试配置验证功能..."
    
    # 加载parse_yaml.sh
    source validation/parse_yaml.sh
    
    # 解析配置
    parse_yaml "validation/configs/config_shared.yaml" "VALID_TEST" false
    
    # 测试必需变量检查
    local required_vars=(
        "device" "network_device" "tensor_parallel_size" "max_num_seqs" 
        "model_name"
    )
    
    if check_yaml_vars "VALID_TEST" "${required_vars[@]}"; then
        print_success "配置验证通过"
    else
        print_error "配置验证失败"
        return 1
    fi
    
    # 测试缺失变量检查
    if check_yaml_vars "VALID_TEST" "nonexistent_var"; then
        print_error "应该检测到缺失变量但没有"
        return 1
    else
        print_success "正确检测到缺失变量"
    fi
    
    return 0
}

# 测试run_cpu_profiling.sh的配置加载部分
test_cpu_profiling_config() {
    print_info "测试run_cpu_profiling.sh的配置加载..."
    
    # 模拟run_cpu_profiling.sh中的配置加载逻辑
    CONFIG_SHARED="validation/configs/config_shared.yaml"
    CONFIG_VLLM="validation/configs/config_vllm.yaml"
    
    # 加载parse_yaml.sh
    source validation/parse_yaml.sh
    
    # 解析配置文件
    if ! parse_yaml "$CONFIG_SHARED" "SHARED"; then
        print_error "无法解析共享配置文件"
        return 1
    fi
    
    if ! parse_yaml "$CONFIG_VLLM" "VLLM"; then
        print_error "无法解析vLLM配置文件"
        return 1
    fi
    
    # 提取配置
    local DEVICE="$SHARED_device"
    local NETWORK_DEVICE="$SHARED_network_device"
    local TENSOR_PARALLEL_SIZE="$SHARED_tensor_parallel_size"
    local MODEL_NAME="$SHARED_model_name"
    local MODEL_PATH="$VLLM_model"
    local MAX_BATCH_SIZE="$SHARED_prediction_max_batch_size"
    
    # 验证配置
    print_info "提取的配置:"
    echo "  设备类型: $DEVICE"
    echo "  网络设备: $NETWORK_DEVICE"
    echo "  张量并行度: $TENSOR_PARALLEL_SIZE"
    echo "  模型名称: $MODEL_NAME"
    echo "  模型路径: $MODEL_PATH"
    echo "  最大批次大小: $MAX_BATCH_SIZE"
    
    # 检查关键配置是否存在
    if [ -z "$DEVICE" ] || [ -z "$NETWORK_DEVICE" ] || [ -z "$MODEL_NAME" ] || [ -z "$MODEL_PATH" ]; then
        print_error "关键配置缺失"
        return 1
    fi
    
    print_success "CPU profiling配置加载测试通过"
    return 0
}

# 主测试函数
main() {
    print_info "开始测试新的配置加载功能..."
    echo ""
    
    # 检查是否在正确的目录
    if [ ! -f "validation/configs/config_shared.yaml" ]; then
        print_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 测试parse_yaml.sh脚本
    if ! test_parse_yaml_script; then
        print_error "parse_yaml.sh脚本测试失败"
        exit 1
    fi
    
    echo ""
    
    # 测试配置验证
    if ! test_config_validation; then
        print_error "配置验证测试失败"
        exit 1
    fi
    
    echo ""
    
    # 测试CPU profiling配置加载
    if ! test_cpu_profiling_config; then
        print_error "CPU profiling配置加载测试失败"
        exit 1
    fi
    
    echo ""
    print_success "所有测试通过！新的配置加载功能正常。"
}

# 运行测试
main "$@"
