#!/bin/bash

# 测试统一YAML解析功能的脚本

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 测试parse_yaml.sh脚本的独立功能
test_parse_yaml_standalone() {
    print_info "测试parse_yaml.sh独立功能..."
    
    if [ ! -f "validation/parse_yaml.sh" ]; then
        print_error "parse_yaml.sh脚本不存在"
        return 1
    fi
    
    # 加载脚本
    source validation/parse_yaml.sh
    
    # 测试解析共享配置
    if parse_yaml "validation/configs/config_shared.yaml" "STANDALONE_SHARED"; then
        print_success "独立解析共享配置成功"
        
        # 检查一些关键变量
        if [ -n "$STANDALONE_SHARED_device" ]; then
            print_info "设备类型: $STANDALONE_SHARED_device"
        fi
        
        if [ -n "$STANDALONE_SHARED_execution_time_predictor_prediction_max_batch_size" ]; then
            print_info "最大批次大小: $STANDALONE_SHARED_execution_time_predictor_prediction_max_batch_size"
        fi
    else
        print_error "独立解析共享配置失败"
        return 1
    fi
    
    return 0
}

# 测试run_vidur.sh的配置解析部分
test_run_vidur_parsing() {
    print_info "测试run_vidur.sh的配置解析..."
    
    # 模拟run_vidur.sh的环境
    SHARED_CONFIG="validation/configs/config_shared.yaml"
    VIDUR_CONFIG="validation/configs/config_vidur.yaml"
    
    # 检查配置文件
    if [ ! -f "$SHARED_CONFIG" ]; then
        print_error "共享配置文件不存在: $SHARED_CONFIG"
        return 1
    fi
    
    if [ ! -f "$VIDUR_CONFIG" ]; then
        print_error "Vidur配置文件不存在: $VIDUR_CONFIG"
        return 1
    fi
    
    # 加载parse_yaml.sh
    source validation/parse_yaml.sh
    
    # 解析配置文件（模拟run_vidur.sh的逻辑）
    if parse_yaml "$SHARED_CONFIG" "VIDUR_TEST_SHARED"; then
        print_success "Vidur共享配置解析成功"
    else
        print_error "Vidur共享配置解析失败"
        return 1
    fi
    
    if parse_yaml "$VIDUR_CONFIG" "VIDUR_TEST_VIDUR"; then
        print_success "Vidur专用配置解析成功"
    else
        print_error "Vidur专用配置解析失败"
        return 1
    fi
    
    # 检查一些关键变量
    print_info "Vidur配置检查:"
    echo "  模型名称: $VIDUR_TEST_SHARED_model_name"
    echo "  设备类型: $VIDUR_TEST_SHARED_device"
    echo "  网络设备: $VIDUR_TEST_SHARED_network_device"
    echo "  张量并行度: $VIDUR_TEST_SHARED_tensor_parallel_size"
    
    return 0
}

# 测试run_vllm.sh的配置解析部分
test_run_vllm_parsing() {
    print_info "测试run_vllm.sh的配置解析..."
    
    # 模拟run_vllm.sh的环境
    SHARED_CONFIG="validation/configs/config_shared.yaml"
    VLLM_CONFIG="validation/configs/config_vllm.yaml"
    
    # 检查配置文件
    if [ ! -f "$SHARED_CONFIG" ]; then
        print_error "共享配置文件不存在: $SHARED_CONFIG"
        return 1
    fi
    
    if [ ! -f "$VLLM_CONFIG" ]; then
        print_error "vLLM配置文件不存在: $VLLM_CONFIG"
        return 1
    fi
    
    # 加载parse_yaml.sh
    source validation/parse_yaml.sh
    
    # 模拟日志函数（简化版）
    log_message() {
        local level=$1
        local message=$2
        echo "[${level}] $message"
    }
    
    # 包装parse_yaml函数以添加日志记录（模拟run_vllm.sh的逻辑）
    parse_yaml_with_logging() {
        local file=$1
        local prefix=$2
        log_message "DEBUG" "解析配置文件: $file (前缀: $prefix)"
        
        if parse_yaml "$file" "$prefix"; then
            log_message "DEBUG" "配置文件解析成功: $file"
            return 0
        else
            log_message "ERROR" "配置文件解析失败: $file"
            return 1
        fi
    }
    
    # 解析配置文件（模拟run_vllm.sh的逻辑）
    if parse_yaml_with_logging "$SHARED_CONFIG" "VLLM_TEST_SHARED"; then
        print_success "vLLM共享配置解析成功"
    else
        print_error "vLLM共享配置解析失败"
        return 1
    fi
    
    if parse_yaml_with_logging "$VLLM_CONFIG" "VLLM_TEST_VLLM"; then
        print_success "vLLM专用配置解析成功"
    else
        print_error "vLLM专用配置解析失败"
        return 1
    fi
    
    # 检查一些关键变量
    print_info "vLLM配置检查:"
    echo "  模型名称: $VLLM_TEST_SHARED_model_name"
    echo "  模型路径: $VLLM_TEST_VLLM_model"
    echo "  设备类型: $VLLM_TEST_SHARED_device"
    echo "  张量并行度: $VLLM_TEST_SHARED_tensor_parallel_size"
    
    return 0
}

# 测试配置一致性
test_config_consistency() {
    print_info "测试配置解析一致性..."
    
    # 加载parse_yaml.sh
    source validation/parse_yaml.sh
    
    # 使用不同前缀解析同一个文件
    parse_yaml "validation/configs/config_shared.yaml" "CONSISTENCY_TEST1"
    parse_yaml "validation/configs/config_shared.yaml" "CONSISTENCY_TEST2"
    
    # 检查关键变量是否一致
    if [ "$CONSISTENCY_TEST1_device" = "$CONSISTENCY_TEST2_device" ] && \
       [ "$CONSISTENCY_TEST1_model_name" = "$CONSISTENCY_TEST2_model_name" ] && \
       [ "$CONSISTENCY_TEST1_tensor_parallel_size" = "$CONSISTENCY_TEST2_tensor_parallel_size" ]; then
        print_success "配置解析一致性测试通过"
        return 0
    else
        print_error "配置解析一致性测试失败"
        return 1
    fi
}

# 主测试函数
main() {
    print_info "开始测试统一YAML解析功能..."
    echo ""
    
    # 检查是否在正确的目录
    if [ ! -f "validation/configs/config_shared.yaml" ]; then
        print_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 测试parse_yaml.sh独立功能
    if ! test_parse_yaml_standalone; then
        print_error "parse_yaml.sh独立功能测试失败"
        exit 1
    fi
    
    echo ""
    
    # 测试run_vidur.sh配置解析
    if ! test_run_vidur_parsing; then
        print_error "run_vidur.sh配置解析测试失败"
        exit 1
    fi
    
    echo ""
    
    # 测试run_vllm.sh配置解析
    if ! test_run_vllm_parsing; then
        print_error "run_vllm.sh配置解析测试失败"
        exit 1
    fi
    
    echo ""
    
    # 测试配置一致性
    if ! test_config_consistency; then
        print_error "配置一致性测试失败"
        exit 1
    fi
    
    echo ""
    print_success "所有测试通过！统一YAML解析功能正常。"
    print_info "现在所有脚本都使用统一的parse_yaml.sh进行配置解析。"
}

# 运行测试
main "$@"
