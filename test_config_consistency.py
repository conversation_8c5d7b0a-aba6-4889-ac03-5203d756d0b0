#!/usr/bin/env python3
"""
测试配置一致性的脚本
验证 run_cpu_profiling.sh 中的配置与 validation/configs 中的配置是否一致
"""

import yaml
import os
import sys

def load_yaml_config(file_path):
    """加载YAML配置文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"无法加载配置文件 {file_path}: {e}")
        return None

def extract_shell_config():
    """从shell脚本中提取配置（简化版本）"""
    # 这里返回shell脚本中设置的关键配置值
    return {
        'device': 'a100',
        'network_device': 'a100_pairwise_nvlink',
        'tensor_parallel_size': 1,
        'pipeline_parallel_size': 1,
        'max_num_seqs': 128,
        'max_num_batched_tokens': 4096,
        'block_size': 16,
        'gpu_memory_utilization': 0.9,
        'max_model_len': 4096,
        'dtype': 'auto',
        'model_name': 'meta-llama/Meta-Llama-3-8B',
        'model_path': '/share_data/llm_weights/Meta-Llama-3-8B',
        'max_batch_size': 512
    }

def check_consistency():
    """检查配置一致性"""
    print("检查配置一致性...")
    
    # 加载配置文件
    shared_config = load_yaml_config('validation/configs/config_shared.yaml')
    vllm_config = load_yaml_config('validation/configs/config_vllm.yaml')
    vidur_config = load_yaml_config('validation/configs/config_vidur.yaml')
    
    if not shared_config:
        print("❌ 无法加载共享配置文件")
        return False
    
    # 获取shell脚本配置
    shell_config = extract_shell_config()
    
    print("✅ 配置文件加载成功")
    
    # 检查关键参数
    checks = []
    
    # 检查模型名称
    if shared_config.get('model_name') == shell_config['model_name']:
        checks.append(("模型名称", True, shared_config.get('model_name')))
    else:
        checks.append(("模型名称", False, f"shared: {shared_config.get('model_name')}, shell: {shell_config['model_name']}"))
    
    # 检查模型路径
    if vllm_config and vllm_config.get('model') == shell_config['model_path']:
        checks.append(("模型路径", True, vllm_config.get('model')))
    else:
        vllm_model = vllm_config.get('model') if vllm_config else 'N/A'
        checks.append(("模型路径", False, f"vllm: {vllm_model}, shell: {shell_config['model_path']}"))
    
    # 检查张量并行度
    if shared_config.get('tensor_parallel_size') == shell_config['tensor_parallel_size']:
        checks.append(("张量并行度", True, shared_config.get('tensor_parallel_size')))
    else:
        checks.append(("张量并行度", False, f"shared: {shared_config.get('tensor_parallel_size')}, shell: {shell_config['tensor_parallel_size']}"))
    
    # 检查最大序列数
    if shared_config.get('max_num_seqs') == shell_config['max_num_seqs']:
        checks.append(("最大序列数", True, shared_config.get('max_num_seqs')))
    else:
        checks.append(("最大序列数", False, f"shared: {shared_config.get('max_num_seqs')}, shell: {shell_config['max_num_seqs']}"))
    
    # 检查最大批次token数
    if shared_config.get('max_num_batched_tokens') == shell_config['max_num_batched_tokens']:
        checks.append(("最大批次Token数", True, shared_config.get('max_num_batched_tokens')))
    else:
        checks.append(("最大批次Token数", False, f"shared: {shared_config.get('max_num_batched_tokens')}, shell: {shell_config['max_num_batched_tokens']}"))
    
    # 检查块大小
    if shared_config.get('block_size') == shell_config['block_size']:
        checks.append(("KV Cache块大小", True, shared_config.get('block_size')))
    else:
        checks.append(("KV Cache块大小", False, f"shared: {shared_config.get('block_size')}, shell: {shell_config['block_size']}"))
    
    # 检查GPU内存利用率
    if shared_config.get('gpu_memory_utilization') == shell_config['gpu_memory_utilization']:
        checks.append(("GPU内存利用率", True, shared_config.get('gpu_memory_utilization')))
    else:
        checks.append(("GPU内存利用率", False, f"shared: {shared_config.get('gpu_memory_utilization')}, shell: {shell_config['gpu_memory_utilization']}"))
    
    # 检查最大模型长度
    if shared_config.get('max_model_len') == shell_config['max_model_len']:
        checks.append(("最大模型长度", True, shared_config.get('max_model_len')))
    else:
        checks.append(("最大模型长度", False, f"shared: {shared_config.get('max_model_len')}, shell: {shell_config['max_model_len']}"))
    
    # 检查网络设备
    if shared_config.get('network_device') == shell_config['network_device']:
        checks.append(("网络设备", True, shared_config.get('network_device')))
    else:
        checks.append(("网络设备", False, f"shared: {shared_config.get('network_device')}, shell: {shell_config['network_device']}"))
    
    # 显示检查结果
    print("\n配置一致性检查结果:")
    print("=" * 50)
    
    all_passed = True
    for name, passed, value in checks:
        status = "✅" if passed else "❌"
        print(f"{status} {name}: {value}")
        if not passed:
            all_passed = False
    
    print("=" * 50)
    
    if all_passed:
        print("🎉 所有配置检查通过！")
        return True
    else:
        print("⚠️  存在配置不一致的问题，请检查并修正")
        return False

def main():
    print("开始检查 run_cpu_profiling.sh 与 validation/configs 的配置一致性...")
    
    # 检查是否在正确的目录
    if not os.path.exists('validation/configs/config_shared.yaml'):
        print("❌ 请在项目根目录运行此脚本")
        return False
    
    return check_consistency()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
